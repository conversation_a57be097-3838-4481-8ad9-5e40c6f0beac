'use client'

import { vinTrekBlockchainService, TrailData, GPSTrack } from '@/lib/blockchain'
import { Trail } from '@/types'

// Convert blockchain TrailData to frontend Trail type
function convertBlockchainTrailToFrontend(blockchainTrail: TrailData): Trail {
  return {
    id: blockchainTrail.id,
    name: blockchainTrail.name,
    location: blockchainTrail.location,
    difficulty: blockchainTrail.difficulty,
    duration: blockchainTrail.duration,
    distance: blockchainTrail.distance,
    price: 0, // Trails are free in VinTrek's freemium model
    rating: 4.5, // Default rating - could be calculated from user feedback
    reviews: 0, // Could be fetched from blockchain reviews
    description: blockchainTrail.description,
    image: `/images/trails/trail-${blockchainTrail.id}.jpg`, // Default image path
    features: ['GPS Tracking', 'NFT Certificate', 'TREK Rewards'],
    coordinates: blockchainTrail.coordinates,
    rewards: {
      trekTokens: blockchainTrail.rewards.trekTokens,
      nftCertificate: blockchainTrail.rewards.nftCertificate,
      experiencePoints: blockchainTrail.rewards.trekTokens * 10
    },
    isPremiumOnly: blockchainTrail.difficulty === 'Expert', // Expert trails require premium
    createdBy: blockchainTrail.createdBy,
    verified: blockchainTrail.verified,
    createdAt: blockchainTrail.createdAt,
    txHash: blockchainTrail.txHash
  }
}

// Fallback mock data for when blockchain is not available
const fallbackTrails: Trail[] = [
  {
    id: 'ella-rock-001',
    name: 'Ella Rock Trail',
    location: 'Ella, Sri Lanka',
    difficulty: 'Moderate',
    duration: '4-5 hours',
    distance: '8 km',
    price: 2500, // Rs 2500 per person
    rating: 4.8,
    reviews: 156,
    description: 'A scenic hike through tea plantations leading to breathtaking views of Ella Gap and the surrounding mountains.',
    image: '/images/trails/ella-rock.jpg',
    features: ['GPS Tracking', 'NFT Certificate', 'TREK Rewards'],
    routes: [
      {
        id: 'ella-main',
        name: 'Main Route',
        description: 'Standard route through tea plantations',
        difficulty: 'Moderate',
        distance: '8 km',
        duration: '4-5 hours',
        coordinates: [
          { lat: 6.8667, lng: 81.0500 },
          { lat: 6.8700, lng: 81.0520 },
          { lat: 6.8750, lng: 81.0550 }
        ],
        elevationGain: 450,
        isUserContributed: false,
        verified: true,
        createdAt: Date.now()
      }
    ],
    defaultRouteId: 'ella-main',
    rewards: {
      trekTokens: 50,
      nftCertificate: true,
      experiencePoints: 500
    },
    maxCapacity: 25,
    currentBookings: 8,
    available: true,
    isPremiumOnly: false,
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 86400000 // 1 day ago
  },
  {
    id: 'adams-peak-002',
    name: 'Adams Peak (Sri Pada)',
    location: 'Ratnapura, Sri Lanka',
    difficulty: 'Hard',
    duration: '6-8 hours',
    distance: '12 km',
    price: 3500, // Rs 3500 per person (harder trail)
    rating: 4.9,
    reviews: 234,
    description: 'Sacred mountain pilgrimage with stunning sunrise views. Challenging night hike to reach the summit.',
    image: '/images/trails/adams-peak.jpg',
    features: ['GPS Tracking', 'NFT Certificate', 'TREK Rewards', 'Night Hiking'],
    coordinates: [
      { lat: 6.8094, lng: 80.4992 },
      { lat: 6.8100, lng: 80.5000 },
      { lat: 6.8110, lng: 80.5010 }
    ],
    rewards: {
      trekTokens: 100,
      nftCertificate: true,
      experiencePoints: 1000
    },
    maxCapacity: 15,
    currentBookings: 12,
    available: true,
    isPremiumOnly: false,
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 172800000 // 2 days ago
  },
  {
    id: 'sigiriya-003',
    name: 'Sigiriya Rock Fortress',
    location: 'Dambulla, Sri Lanka',
    difficulty: 'Moderate',
    duration: '3-4 hours',
    distance: '5 km',
    price: 4000, // Rs 4000 per person (premium historical site)
    rating: 4.7,
    reviews: 189,
    description: 'Ancient rock fortress with historical significance and panoramic views of the surrounding landscape.',
    image: '/images/trails/sigiriya.jpg',
    features: ['GPS Tracking', 'NFT Certificate', 'TREK Rewards', 'Historical Site'],
    coordinates: [
      { lat: 7.9568, lng: 80.7603 },
      { lat: 7.9570, lng: 80.7605 },
      { lat: 7.9575, lng: 80.7610 }
    ],
    rewards: {
      trekTokens: 75,
      nftCertificate: true,
      experiencePoints: 750
    },
    maxCapacity: 30,
    currentBookings: 5,
    available: true,
    isPremiumOnly: true, // Premium trail
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 259200000 // 3 days ago
  },
  {
    id: 'hidden-waterfall-004',
    name: 'Hidden Waterfall Trail',
    location: 'Kandy, Sri Lanka',
    difficulty: 'Moderate',
    duration: '3-4 hours',
    distance: '6.8 km',
    price: 2000, // Rs 2000 per person
    rating: 4.6,
    reviews: 42,
    description: 'A beautiful hidden waterfall discovered by local hikers. This trail leads through dense forest to a stunning 40-meter waterfall with natural swimming pools.',
    image: '/images/trails/hidden-waterfall.jpg',
    features: ['GPS Tracking', 'NFT Certificate', 'TREK Rewards', 'Waterfall', 'Swimming'],
    coordinates: [
      { lat: 7.2906, lng: 80.6337 },
      { lat: 7.2926, lng: 80.6357 },
      { lat: 7.2956, lng: 80.6387 }
    ],
    rewards: {
      trekTokens: 40,
      nftCertificate: true,
      experiencePoints: 400
    },
    isPremiumOnly: false,
    isUserContributed: true,
    contributedBy: 'addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3n0d3vllmyqwsx5wktcd8cc3sq835lu7drv2xwl2wywfgse35a3x',
    contributedByName: 'Hiking_Explorer_LK',
    createdBy: 'user',
    verified: true,
    createdAt: Date.now() - 86400000 // 1 day ago
  },
  {
    id: 'horton-plains-003',
    name: 'Horton Plains National Park',
    location: 'Nuwara Eliya, Sri Lanka',
    difficulty: 'Easy',
    duration: '3-4 hours',
    distance: '9.5 km',
    price: 0,
    rating: 4.6,
    reviews: 89,
    description: 'UNESCO World Heritage site featuring World\'s End cliff and Baker\'s Falls.',
    image: '/images/trails/horton-plains.jpg',
    features: ['World\'s End', 'Baker\'s Falls', 'Wildlife Viewing'],
    coordinates: [
      { lat: 6.8069, lng: 80.7906 },
      { lat: 6.8079, lng: 80.7916 },
      { lat: 6.8089, lng: 80.7926 },
      { lat: 6.8099, lng: 80.7936 }
    ],
    rewards: {
      trekTokens: 60,
      nftCertificate: true,
      experiencePoints: 600
    },
    isPremiumOnly: false,
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 259200000, // 3 days ago
    available: false // Currently booked out
  },
  {
    id: 'sigiriya-rock-004',
    name: 'Sigiriya Rock Fortress',
    location: 'Dambulla, Sri Lanka',
    difficulty: 'Hard',
    duration: '3-4 hours',
    distance: '3.5 km',
    price: 0,
    rating: 4.9,
    reviews: 234,
    description: 'Ancient rock fortress with stunning views and historical significance. Climb the famous Lion Rock.',
    image: '/images/trails/sigiriya.jpg',
    features: ['Historical Site', 'Rock Climbing', 'Panoramic Views', 'UNESCO Heritage'],
    coordinates: [
      { lat: 7.9568, lng: 80.7592 },
      { lat: 7.9578, lng: 80.7602 },
      { lat: 7.9588, lng: 80.7612 },
      { lat: 7.9598, lng: 80.7622 }
    ],
    rewards: {
      trekTokens: 85,
      nftCertificate: true,
      experiencePoints: 850
    },
    isPremiumOnly: false,
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 432000000, // 5 days ago
    available: true // Available for booking!
  },
  {
    id: 'knuckles-range-005',
    name: 'Knuckles Mountain Range',
    location: 'Matale, Sri Lanka',
    difficulty: 'Expert',
    duration: '6-8 hours',
    distance: '15.2 km',
    price: 0,
    rating: 4.7,
    reviews: 89,
    description: 'Challenging trek through the UNESCO World Heritage Knuckles Range with diverse ecosystems.',
    image: '/images/trails/knuckles.jpg',
    features: ['UNESCO Heritage', 'Biodiversity', 'Cloud Forest', 'Wildlife'],
    coordinates: [
      { lat: 7.4500, lng: 80.7833 },
      { lat: 7.4510, lng: 80.7843 },
      { lat: 7.4520, lng: 80.7853 },
      { lat: 7.4530, lng: 80.7863 }
    ],
    rewards: {
      trekTokens: 120,
      nftCertificate: true,
      experiencePoints: 1200
    },
    isPremiumOnly: true, // Premium trail
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 259200000, // 3 days ago
    available: true // Available for booking!
  },
  {
    id: 'pidurangala-rock-006',
    name: 'Pidurangala Rock',
    location: 'Sigiriya, Sri Lanka',
    difficulty: 'Moderate',
    duration: '2-3 hours',
    distance: '4.8 km',
    price: 0,
    rating: 4.6,
    reviews: 156,
    description: 'Alternative viewpoint to Sigiriya with fewer crowds and spectacular sunrise views.',
    image: '/images/trails/pidurangala.jpg',
    features: ['Sunrise Views', 'Rock Climbing', 'Photography', 'Less Crowded'],
    coordinates: [
      { lat: 7.9669, lng: 80.7575 },
      { lat: 7.9679, lng: 80.7585 },
      { lat: 7.9689, lng: 80.7595 },
      { lat: 7.9699, lng: 80.7605 }
    ],
    rewards: {
      trekTokens: 65,
      nftCertificate: true,
      experiencePoints: 650
    },
    isPremiumOnly: false,
    createdBy: 'system',
    verified: true,
    createdAt: Date.now() - 172800000, // 2 days ago
    available: true // Available for booking!
  }
]

export class TrailService {
  private useBlockchain: boolean = false // Force fallback data for testing
  private blockchainCheckPromise: Promise<void> | null = null

  constructor() {
    // Start blockchain availability check (don't await in constructor)
    this.blockchainCheckPromise = this.checkBlockchainAvailability()
  }

  private async checkBlockchainAvailability(): Promise<void> {
    try {
      // Check if API key is configured
      const apiKey = process.env.NEXT_PUBLIC_BLOCKFROST_PROJECT_ID
      if (!apiKey || apiKey === 'testnetYourProjectIdHere' || apiKey.trim() === '') {
        console.log('🔧 Blockfrost API key not configured, using fallback data')
        this.useBlockchain = false
        return
      }

      // Test if we can connect to Blockfrost API
      const response = await fetch('https://cardano-testnet.blockfrost.io/api/v0/health', {
        headers: {
          'project_id': apiKey
        }
      })
      this.useBlockchain = response.ok

      if (this.useBlockchain) {
        console.log('✅ Blockchain connection established')
      } else {
        console.log('❌ Blockchain connection failed, using fallback data')
      }
    } catch (error) {
      console.warn('Blockchain not available, using fallback data:', error)
      this.useBlockchain = false
    }
  }

  // Get all trails (from blockchain or fallback)
  async getTrails(): Promise<Trail[]> {
    try {
      // Wait for blockchain availability check to complete
      if (this.blockchainCheckPromise) {
        await this.blockchainCheckPromise
        this.blockchainCheckPromise = null
      }

      if (this.useBlockchain) {
        console.log('🔗 Fetching trails from Cardano blockchain...')
        const blockchainTrails = await vinTrekBlockchainService.getTrailsFromChain()
        
        if (blockchainTrails.length > 0) {
          console.log(`✅ Found ${blockchainTrails.length} trails on blockchain`)
          return blockchainTrails.map(convertBlockchainTrailToFrontend)
        } else {
          console.log('📝 No trails found on blockchain, using fallback data')
          console.log('Fallback trails count:', fallbackTrails.length)
          console.log('User contributed trails:', fallbackTrails.filter(t => t.isUserContributed))
          return fallbackTrails
        }
      } else {
        console.log('💾 Using fallback trail data (blockchain unavailable)')
        console.log('Fallback trails count:', fallbackTrails.length)
        console.log('User contributed trails:', fallbackTrails.filter(t => t.isUserContributed))
        return fallbackTrails
      }
    } catch (error) {
      console.error('Error fetching trails:', error)
      console.log('💾 Falling back to mock data due to error')
      return fallbackTrails
    }
  }

  // Get trail by ID
  async getTrailById(trailId: string): Promise<Trail | null> {
    try {
      if (this.useBlockchain) {
        const blockchainTrail = await vinTrekBlockchainService.getTrailById(trailId)
        if (blockchainTrail) {
          return convertBlockchainTrailToFrontend(blockchainTrail)
        }
      }
      
      // Fallback to mock data
      return fallbackTrails.find(trail => trail.id === trailId) || null
    } catch (error) {
      console.error('Error fetching trail by ID:', error)
      return fallbackTrails.find(trail => trail.id === trailId) || null
    }
  }

  // Store new trail on blockchain
  async storeTrail(trailData: Omit<TrailData, 'txHash'>): Promise<string> {
    if (!this.useBlockchain) {
      throw new Error('Blockchain not available for storing trails')
    }

    try {
      console.log('🔗 Storing trail on Cardano blockchain...')
      const txHash = await vinTrekBlockchainService.storeTrailOnChain(trailData)
      console.log('✅ Trail stored successfully:', txHash)
      return txHash
    } catch (error) {
      console.error('Error storing trail on blockchain:', error)
      throw error
    }
  }

  // Store GPS track on blockchain
  async storeGPSTrack(gpsTrack: Omit<GPSTrack, 'txHash'>): Promise<string> {
    if (!this.useBlockchain) {
      throw new Error('Blockchain not available for storing GPS tracks')
    }

    try {
      console.log('🔗 Storing GPS track on Cardano blockchain...')
      const txHash = await vinTrekBlockchainService.storeGPSTrack(gpsTrack)
      console.log('✅ GPS track stored successfully:', txHash)
      return txHash
    } catch (error) {
      console.error('Error storing GPS track on blockchain:', error)
      throw error
    }
  }

  // Get user's GPS tracks
  async getUserGPSTracks(userAddress: string): Promise<GPSTrack[]> {
    try {
      if (this.useBlockchain) {
        return await vinTrekBlockchainService.getGPSTracksByUser(userAddress)
      }
      return []
    } catch (error) {
      console.error('Error fetching user GPS tracks:', error)
      return []
    }
  }

  // Verify trail completion and reward tokens
  async completeTrail(trailId: string, gpsTrack: GPSTrack): Promise<{ verified: boolean; rewardTxHash?: string }> {
    try {
      if (!this.useBlockchain) {
        throw new Error('Blockchain not available for trail completion')
      }

      console.log('🔗 Verifying trail completion on blockchain...')
      const verified = await vinTrekBlockchainService.verifyTrailCompletion(trailId, gpsTrack)
      
      if (verified && gpsTrack.userAddress) {
        console.log('✅ Trail completion verified, rewarding TREK tokens...')
        const rewardTxHash = await vinTrekBlockchainService.rewardTrekTokens(trailId, gpsTrack.userAddress, gpsTrack)
        return { verified: true, rewardTxHash }
      }

      return { verified }
    } catch (error) {
      console.error('Error completing trail:', error)
      throw error
    }
  }
}

// Export singleton instance
export const trailService = new TrailService()
