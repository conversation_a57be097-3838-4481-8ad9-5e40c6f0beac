// VinTrek Email Service
// Handles booking confirmations and notifications

interface BookingConfirmation {
  confirmationId: string
  trailName: string
  date: string
  participants: number
  totalPrice: number
  userEmail: string
  userName: string
}

interface EmailTemplate {
  subject: string
  html: string
  text: string
}

class EmailService {
  private isProduction = process.env.NODE_ENV === 'production'

  // Generate booking confirmation email template
  private generateBookingConfirmationEmail(booking: BookingConfirmation): EmailTemplate {
    const subject = `🎉 VinTrek Booking Confirmed - ${booking.trailName}`
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Booking Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
            .booking-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e5e7eb; }
            .detail-row:last-child { border-bottom: none; }
            .total { font-weight: bold; font-size: 18px; color: #10b981; }
            .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
            .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏔️ VinTrek</h1>
              <h2>Booking Confirmed!</h2>
            </div>
            <div class="content">
              <p>Dear ${booking.userName},</p>
              <p>Your trail booking has been successfully confirmed! Get ready for an amazing adventure.</p>
              
              <div class="booking-details">
                <h3>Booking Details</h3>
                <div class="detail-row">
                  <span>Confirmation ID:</span>
                  <span><strong>${booking.confirmationId}</strong></span>
                </div>
                <div class="detail-row">
                  <span>Trail:</span>
                  <span>${booking.trailName}</span>
                </div>
                <div class="detail-row">
                  <span>Date:</span>
                  <span>${booking.date}</span>
                </div>
                <div class="detail-row">
                  <span>Participants:</span>
                  <span>${booking.participants} ${booking.participants === 1 ? 'person' : 'people'}</span>
                </div>
                <div class="detail-row total">
                  <span>Total Amount:</span>
                  <span>₨${booking.totalPrice.toLocaleString()}</span>
                </div>
              </div>

              <h3>What's Next?</h3>
              <ul>
                <li>📱 Download the VinTrek mobile app for GPS navigation</li>
                <li>🎒 Check the trail requirements and pack accordingly</li>
                <li>🌤️ Monitor weather conditions before your trip</li>
                <li>💰 Earn TREK tokens and NFT certificates upon completion</li>
              </ul>

              <a href="${process.env.NEXT_PUBLIC_APP_URL}/bookings" class="button">
                View My Bookings
              </a>

              <div class="footer">
                <p>Need help? Contact <NAME_EMAIL></p>
                <p>© 2024 VinTrek. All rights reserved.</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `

    const text = `
VinTrek Booking Confirmation

Dear ${booking.userName},

Your trail booking has been successfully confirmed!

Booking Details:
- Confirmation ID: ${booking.confirmationId}
- Trail: ${booking.trailName}
- Date: ${booking.date}
- Participants: ${booking.participants} ${booking.participants === 1 ? 'person' : 'people'}
- Total Amount: ₨${booking.totalPrice.toLocaleString()}

What's Next?
- Download the VinTrek mobile app for GPS navigation
- Check the trail requirements and pack accordingly
- Monitor weather conditions before your trip
- Earn TREK tokens and NFT certificates upon completion

View your bookings: ${process.env.NEXT_PUBLIC_APP_URL}/bookings

Need help? Contact <NAME_EMAIL>

© 2024 VinTrek. All rights reserved.
    `

    return { subject, html, text }
  }

  // Send booking confirmation email
  async sendBookingConfirmation(booking: BookingConfirmation): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.isProduction) {
        // In development, simulate email sending and show in console
        console.log('📧 Email Service (Development Mode)')
        console.log('='.repeat(50))
        console.log(`To: ${booking.userEmail}`)
        console.log(`Subject: 🎉 VinTrek Booking Confirmed - ${booking.trailName}`)
        console.log('='.repeat(50))
        console.log(`
Dear ${booking.userName},

Your trail booking has been successfully confirmed!

Booking Details:
- Confirmation ID: ${booking.confirmationId}
- Trail: ${booking.trailName}
- Date: ${booking.date}
- Participants: ${booking.participants} ${booking.participants === 1 ? 'person' : 'people'}
- Total Amount: ₨${booking.totalPrice.toLocaleString()}

What's Next?
- Download the VinTrek mobile app for GPS navigation
- Check the trail requirements and pack accordingly
- Monitor weather conditions before your trip
- Earn TREK tokens and NFT certificates upon completion

View your bookings: ${process.env.NEXT_PUBLIC_APP_URL}/bookings

Need help? Contact <NAME_EMAIL>
        `)
        console.log('='.repeat(50))

        // Store email in localStorage for demo purposes
        const sentEmails = JSON.parse(localStorage.getItem('vintrek_sent_emails') || '[]')
        sentEmails.push({
          ...booking,
          sentAt: new Date().toISOString(),
          emailTemplate: this.generateBookingConfirmationEmail(booking)
        })
        localStorage.setItem('vintrek_sent_emails', JSON.stringify(sentEmails))

        return { success: true }
      }

      // In production, use actual email service (e.g., SendGrid, Nodemailer, etc.)
      const emailTemplate = this.generateBookingConfirmationEmail(booking)
      
      // This would be replaced with actual email service API call
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: booking.userEmail,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send email')
      }

      return { success: true }
    } catch (error) {
      console.error('Error sending booking confirmation email:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      }
    }
  }

  // Get sent emails (for demo purposes)
  getSentEmails(): any[] {
    try {
      return JSON.parse(localStorage.getItem('vintrek_sent_emails') || '[]')
    } catch (error) {
      console.error('Error loading sent emails:', error)
      return []
    }
  }

  // Clear sent emails (for demo purposes)
  clearSentEmails(): void {
    localStorage.removeItem('vintrek_sent_emails')
  }
}

export const emailService = new EmailService()
export type { BookingConfirmation }
