'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Mountain, ArrowLeft, Wallet, Settings, AlertTriangle, CheckCircle } from 'lucide-react'
import { WalletTester } from '@/components/wallet/WalletTester'
import { WalletDebug } from '@/components/wallet/WalletDebug'

export default function WalletTestPage() {
  const [activeTab, setActiveTab] = useState<'tester' | 'debug' | 'setup'>('tester')
  const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true'

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
              <Mountain className="h-8 w-8 text-green-600" />
              <span className="text-2xl font-bold text-gray-900">VinTrek</span>
            </Link>
            <div className="text-sm text-gray-600">
              Wallet Testing & Setup
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Wallet Testing & Setup</h1>
          <p className="text-gray-600">
            Test real Cardano wallet connections and configure blockchain features.
          </p>
        </div>

        {/* Mode Indicator */}
        <div className={`mb-6 p-4 rounded-lg border ${
          isDemoMode 
            ? 'bg-yellow-50 border-yellow-200' 
            : 'bg-green-50 border-green-200'
        }`}>
          <div className="flex items-center space-x-2">
            {isDemoMode ? (
              <>
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <span className="font-medium text-yellow-800">Demo Mode Active</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Real Mode Active</span>
              </>
            )}
          </div>
          <p className={`text-sm mt-1 ${
            isDemoMode ? 'text-yellow-700' : 'text-green-700'
          }`}>
            {isDemoMode 
              ? 'Wallet connections are simulated. Set NEXT_PUBLIC_DEMO_MODE=false for real testing.'
              : 'Real wallet connections enabled. Make sure you have a Cardano wallet installed.'
            }
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg shadow-md p-1 flex space-x-1">
            {[
              { id: 'tester', label: 'Wallet Tester', icon: Wallet },
              { id: 'debug', label: 'Debug Info', icon: Settings },
              { id: 'setup', label: 'Setup Guide', icon: CheckCircle }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-green-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'tester' && <WalletTester />}
        
        {activeTab === 'debug' && <WalletDebug />}
        
        {activeTab === 'setup' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Real Wallet Setup Guide</h2>
            
            <div className="space-y-6">
              {/* Step 1 */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Step 1: Install a Cardano Wallet</h3>
                <p className="text-gray-600 mb-3">
                  Choose and install one of these recommended Cardano wallets:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { name: 'Lace', url: 'https://www.lace.io/', desc: 'Official IOG wallet, user-friendly' },
                    { name: 'Eternl', url: 'https://eternl.io/', desc: 'Feature-rich, advanced users' },
                    { name: 'Nami', url: 'https://namiwallet.io/', desc: 'Simple, lightweight wallet' },
                    { name: 'Flint', url: 'https://flint-wallet.com/', desc: 'Mobile-first design' }
                  ].map((wallet) => (
                    <a
                      key={wallet.name}
                      href={wallet.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                    >
                      <div className="font-medium text-gray-900">{wallet.name}</div>
                      <div className="text-sm text-gray-600">{wallet.desc}</div>
                    </a>
                  ))}
                </div>
              </div>

              {/* Step 2 */}
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Step 2: Configure for Testnet</h3>
                <p className="text-gray-600 mb-3">
                  Set your wallet to testnet mode for safe testing:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Open your wallet settings</li>
                  <li>Switch to &quot;Testnet&quot; or &quot;Preview&quot; network</li>
                  <li>Create or restore a testnet wallet</li>
                  <li>Get testnet ADA from the faucet</li>
                </ul>
              </div>

              {/* Step 3 */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Step 3: Get Testnet ADA</h3>
                <p className="text-gray-600 mb-3">
                  Get free testnet ADA for testing transactions:
                </p>
                <a
                  href="https://docs.cardano.org/cardano-testnet/tools/faucet"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <span>Cardano Testnet Faucet</span>
                  <ArrowLeft className="h-4 w-4 rotate-180" />
                </a>
              </div>

              {/* Step 4 */}
              <div className="border-l-4 border-orange-500 pl-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Step 4: Configure VinTrek</h3>
                <p className="text-gray-600 mb-3">
                  Update your environment configuration:
                </p>
                <div className="bg-gray-100 p-4 rounded-lg">
                  <code className="text-sm">
                    # In .env.local<br/>
                    NEXT_PUBLIC_DEMO_MODE=false<br/>
                    NEXT_PUBLIC_BLOCKFROST_PROJECT_ID=your_testnet_project_id
                  </code>
                </div>
              </div>

              {/* Step 5 */}
              <div className="border-l-4 border-red-500 pl-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Step 5: Test Connection</h3>
                <p className="text-gray-600 mb-3">
                  Use the Wallet Tester tab to verify everything works:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Restart the application after config changes</li>
                  <li>Click &quot;Scan Wallets&quot; to detect installed wallets</li>
                  <li>Click &quot;Test&quot; next to your wallet to connect</li>
                  <li>Verify address and balance display correctly</li>
                </ul>
              </div>
            </div>

            {/* Warning */}
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <span className="font-medium text-red-800">Important Security Notes</span>
              </div>
              <ul className="text-sm text-red-700 mt-2 space-y-1">
                <li>• Only use testnet for development and testing</li>
                <li>• Never share your seed phrase or private keys</li>
                <li>• Always verify transaction details before signing</li>
                <li>• Use small amounts for initial testing</li>
              </ul>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 flex justify-center space-x-4">
          <Link
            href="/demo"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            View Blockchain Demo
          </Link>
          <Link
            href="/trails"
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
          >
            Test with Trails
          </Link>
        </div>
      </div>
    </div>
  )
}
