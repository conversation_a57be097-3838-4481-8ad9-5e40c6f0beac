'use client'

import { useState, useEffect } from 'react'
import { Mountain, ArrowLeft, CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/components/providers/AuthProvider'
import { useWallet } from '@/components/providers/WalletProvider'
import { useRealTimeDashboard } from '@/hooks/useRealTimeDashboard'
import { emailService } from '@/services/emailService'

interface TestResult {
  name: string
  status: 'pass' | 'fail' | 'pending'
  message: string
}

export default function TestFixesPage() {
  const { user, isAuthenticated, login } = useAuth()
  const { connected } = useWallet()
  const { stats, refresh, isDataFresh } = useRealTimeDashboard()
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const updateTestResult = (name: string, status: 'pass' | 'fail', message: string) => {
    setTestResults(prev => {
      const existing = prev.find(t => t.name === name)
      if (existing) {
        existing.status = status
        existing.message = message
        return [...prev]
      }
      return [...prev, { name, status, message }]
    })
  }

  const runTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Authentication System
    updateTestResult('Authentication System', 'pending', 'Testing...')
    try {
      if (isAuthenticated && user) {
        updateTestResult('Authentication System', 'pass', `User logged in: ${user.email}`)
      } else {
        updateTestResult('Authentication System', 'fail', 'User not authenticated')
      }
    } catch (error) {
      updateTestResult('Authentication System', 'fail', `Error: ${error}`)
    }

    // Test 2: Wallet Connection
    updateTestResult('Wallet Connection', 'pending', 'Testing...')
    try {
      if (connected) {
        updateTestResult('Wallet Connection', 'pass', 'Wallet connected successfully')
      } else {
        updateTestResult('Wallet Connection', 'fail', 'Wallet not connected')
      }
    } catch (error) {
      updateTestResult('Wallet Connection', 'fail', `Error: ${error}`)
    }

    // Test 3: Trail Pricing
    updateTestResult('Trail Pricing', 'pending', 'Testing...')
    try {
      // Simulate trail data check
      const mockTrail = {
        id: 'test-trail',
        name: 'Test Trail',
        price: 2500,
        maxCapacity: 20,
        currentBookings: 5
      }
      
      const trailPrice = mockTrail.price > 0 ? mockTrail.price : 2500
      const availableSpots = Math.max(1, mockTrail.maxCapacity - mockTrail.currentBookings)
      
      if (trailPrice > 0 && availableSpots > 0) {
        updateTestResult('Trail Pricing', 'pass', `Price: ₨${trailPrice}, Available spots: ${availableSpots}`)
      } else {
        updateTestResult('Trail Pricing', 'fail', 'Pricing or availability issue')
      }
    } catch (error) {
      updateTestResult('Trail Pricing', 'fail', `Error: ${error}`)
    }

    // Test 4: Email Service
    updateTestResult('Email Service', 'pending', 'Testing...')
    try {
      if (user) {
        const emailResult = await emailService.sendBookingConfirmation({
          confirmationId: 'TEST123',
          trailName: 'Test Trail',
          date: '2025-07-10',
          participants: 2,
          totalPrice: 5000,
          userEmail: user.email,
          userName: user.name
        })
        
        if (emailResult.success) {
          updateTestResult('Email Service', 'pass', 'Email sent successfully')
        } else {
          updateTestResult('Email Service', 'fail', `Email failed: ${emailResult.error}`)
        }
      } else {
        updateTestResult('Email Service', 'fail', 'No user to send email to')
      }
    } catch (error) {
      updateTestResult('Email Service', 'fail', `Error: ${error}`)
    }

    // Test 5: Real-time Dashboard
    updateTestResult('Real-time Dashboard', 'pending', 'Testing...')
    try {
      const dashboardWorking = stats && typeof stats.totalTrails === 'number'
      if (dashboardWorking) {
        updateTestResult('Real-time Dashboard', 'pass', 
          `Stats loaded: ${stats.totalTrails} trails, ${stats.totalBookings} bookings, Data fresh: ${isDataFresh()}`
        )
      } else {
        updateTestResult('Real-time Dashboard', 'fail', 'Dashboard stats not loading')
      }
    } catch (error) {
      updateTestResult('Real-time Dashboard', 'fail', `Error: ${error}`)
    }

    // Test 6: Local Storage
    updateTestResult('Local Storage', 'pending', 'Testing...')
    try {
      // Test booking storage
      const testBooking = {
        id: 'test-booking-' + Date.now(),
        trailName: 'Test Trail',
        date: '2025-07-10',
        participants: 2,
        totalPrice: 5000,
        bookedAt: new Date().toISOString(),
        status: 'confirmed'
      }
      
      const existingBookings = JSON.parse(localStorage.getItem('vintrek_bookings') || '[]')
      existingBookings.push(testBooking)
      localStorage.setItem('vintrek_bookings', JSON.stringify(existingBookings))
      
      // Verify it was saved
      const savedBookings = JSON.parse(localStorage.getItem('vintrek_bookings') || '[]')
      const testBookingExists = savedBookings.find((b: any) => b.id === testBooking.id)
      
      if (testBookingExists) {
        updateTestResult('Local Storage', 'pass', 'Booking saved and retrieved successfully')
      } else {
        updateTestResult('Local Storage', 'fail', 'Booking not saved properly')
      }
    } catch (error) {
      updateTestResult('Local Storage', 'fail', `Error: ${error}`)
    }

    // Test 7: Navigation
    updateTestResult('Navigation', 'pending', 'Testing...')
    try {
      const bookingsPageExists = document.querySelector('a[href="/bookings"]') !== null
      if (bookingsPageExists) {
        updateTestResult('Navigation', 'pass', 'Bookings navigation link exists')
      } else {
        updateTestResult('Navigation', 'fail', 'Bookings navigation link missing')
      }
    } catch (error) {
      updateTestResult('Navigation', 'fail', `Error: ${error}`)
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: 'pass' | 'fail' | 'pending') => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'fail':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'pending':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
    }
  }

  const getStatusColor = (status: 'pass' | 'fail' | 'pending') => {
    switch (status) {
      case 'pass':
        return 'bg-green-50 border-green-200'
      case 'fail':
        return 'bg-red-50 border-red-200'
      case 'pending':
        return 'bg-blue-50 border-blue-200'
    }
  }

  const passedTests = testResults.filter(t => t.status === 'pass').length
  const totalTests = testResults.length
  const allTestsPassed = totalTests > 0 && passedTests === totalTests

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
              <Mountain className="h-8 w-8 text-green-600" />
              <span className="text-2xl font-bold text-gray-900">VinTrek</span>
            </Link>
            <div className="text-sm text-gray-600">
              Test Fixes
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">VinTrek Fixes Test Suite</h1>
          <p className="text-gray-600">
            Testing all the implemented fixes for participant selection, pricing, authentication, email, and real-time updates.
          </p>
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Test Results</h2>
            <button
              onClick={runTests}
              disabled={isRunning}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                isRunning
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              <RefreshCw className={`h-4 w-4 ${isRunning ? 'animate-spin' : ''}`} />
              <span>{isRunning ? 'Running Tests...' : 'Run All Tests'}</span>
            </button>
          </div>

          {totalTests > 0 && (
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Progress: {passedTests}/{totalTests} tests passed</span>
                <span>{Math.round((passedTests / totalTests) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    allTestsPassed ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${(passedTests / totalTests) * 100}%` }}
                />
              </div>
            </div>
          )}

          {allTestsPassed && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">All tests passed! 🎉</span>
              </div>
              <p className="text-sm text-green-700 mt-1">
                All VinTrek fixes are working correctly.
              </p>
            </div>
          )}
        </div>

        {/* Test Results */}
        <div className="space-y-4">
          {testResults.map((test, index) => (
            <div key={index} className={`border rounded-lg p-4 ${getStatusColor(test.status)}`}>
              <div className="flex items-center space-x-3">
                {getStatusIcon(test.status)}
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{test.name}</h3>
                  <p className="text-sm text-gray-600 mt-1">{test.message}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {testResults.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            <AlertCircle className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg mb-2">No tests run yet</p>
            <p>Click &quot;Run All Tests&quot; to start testing the fixes</p>
          </div>
        )}

        {/* User Status */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-700">Authentication:</span>
              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {isAuthenticated ? `Logged in as ${user?.name}` : 'Not logged in'}
              </span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-700">Wallet:</span>
              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {connected ? 'Connected' : 'Not connected'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
