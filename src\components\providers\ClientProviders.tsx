'use client'

import { ReactNode } from 'react'
import { WalletProvider } from './WalletProvider'
import { AuthProvider } from './AuthProvider'
import { ErrorBoundary } from '@/components/ui/ErrorBoundary'

interface ClientProvidersProps {
  children: ReactNode
}

export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <WalletProvider>
          {children}
        </WalletProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}
