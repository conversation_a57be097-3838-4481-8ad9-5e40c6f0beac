'use client'

import { useState, useEffect, useRef } from 'react'
import { Play, Pause, Square, MapPin, Clock, Navigation, Zap, Trophy, AlertCircle, Wifi, WifiOff } from 'lucide-react'
import { useTrailRecording } from '@/hooks/useTrailRecording'
import { useAuth } from '@/components/providers/AuthProvider'
import { formatDistance, formatDuration, formatSpeed } from '@/lib/trailUtils'
import { CompletedTrail } from '@/types/trail'

interface RealTimeTrailRecorderProps {
  onTrailCompleted?: (trail: CompletedTrail) => void
  className?: string
}

export function RealTimeTrailRecorder({ onTrailCompleted, className = '' }: RealTimeTrailRecorderProps) {
  const { user, isAuthenticated } = useAuth()
  const [showStartModal, setShowStartModal] = useState(false)
  const [showCompletionModal, setShowCompletionModal] = useState(false)
  const [trailName, setTrailName] = useState('')
  const [trailDescription, setTrailDescription] = useState('')
  const [isOnline, setIsOnline] = useState(true)
  const [lastLocationUpdate, setLastLocationUpdate] = useState<Date | null>(null)
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const {
    recording,
    isRecording,
    isPaused,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    getCurrentStats,
  } = useTrailRecording()

  const stats = getCurrentStats()

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Real-time updates when recording
  useEffect(() => {
    if (isRecording && !isPaused) {
      updateIntervalRef.current = setInterval(() => {
        setLastLocationUpdate(new Date())
        // Force re-render to update stats
      }, 1000)
    } else {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current)
        updateIntervalRef.current = null
      }
    }

    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current)
      }
    }
  }, [isRecording, isPaused])

  const handleStartRecording = async () => {
    if (!isAuthenticated) {
      alert('Please sign in to record trails')
      return
    }

    if (!trailName.trim()) {
      alert('Please enter a trail name')
      return
    }

    const success = await startRecording(trailName.trim(), trailDescription.trim() || undefined)
    if (success) {
      setShowStartModal(false)
      setTrailName('')
      setTrailDescription('')
    } else {
      alert('Failed to start recording. Please check location permissions.')
    }
  }

  const handleStopRecording = async () => {
    const recordingData = stopRecording()
    if (recordingData) {
      // Save completed trail
      const completedTrail: CompletedTrail = {
        id: recordingData.id,
        name: recordingData.name,
        description: recordingData.description,
        location: 'GPS Recorded',
        difficulty: 'Moderate', // Could be calculated based on elevation/distance
        completedAt: new Date().toISOString(),
        duration: recordingData.totalDuration,
        distance: recordingData.totalDistance,
        coordinates: recordingData.coordinates,
        walletAddress: user?.email || 'anonymous',
        nftMinted: false,
        trekTokensEarned: Math.floor(recordingData.totalDistance / 1000) * 10, // 10 tokens per km
        verified: true
      }

      // Save to localStorage
      const existingTrails = JSON.parse(localStorage.getItem('vintrek_completed_trails') || '[]')
      existingTrails.push(completedTrail)
      localStorage.setItem('vintrek_completed_trails', JSON.stringify(existingTrails))

      // Trigger callback
      onTrailCompleted?.(completedTrail)

      setShowCompletionModal(true)
    }
  }

  const getStatusColor = () => {
    if (!isRecording) return 'text-gray-500'
    if (isPaused) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getStatusText = () => {
    if (!isRecording) return 'Ready to Record'
    if (isPaused) return 'Recording Paused'
    return 'Recording Active'
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Trail Recorder</h2>
        <div className="flex items-center space-x-2">
          {isOnline ? (
            <Wifi className="h-5 w-5 text-green-500" />
          ) : (
            <WifiOff className="h-5 w-5 text-red-500" />
          )}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
      </div>

      {/* Real-time Stats */}
      {isRecording && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <MapPin className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <div className="text-lg font-bold text-blue-900">
              {formatDistance(stats.totalDistance)}
            </div>
            <div className="text-xs text-blue-600">Distance</div>
          </div>

          <div className="bg-green-50 rounded-lg p-4 text-center">
            <Clock className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <div className="text-lg font-bold text-green-900">
              {formatDuration(stats.totalDuration)}
            </div>
            <div className="text-xs text-green-600">Duration</div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4 text-center">
            <Zap className="h-6 w-6 text-purple-600 mx-auto mb-2" />
            <div className="text-lg font-bold text-purple-900">
              {formatSpeed(stats.averageSpeed)}
            </div>
            <div className="text-xs text-purple-600">Avg Speed</div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4 text-center">
            <Navigation className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
            <div className="text-lg font-bold text-yellow-900">
              {stats.elevationGain.toFixed(0)}m
            </div>
            <div className="text-xs text-yellow-600">Elevation</div>
          </div>
        </div>
      )}

      {/* Last Update Info */}
      {isRecording && lastLocationUpdate && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Last GPS Update:</span>
            <span>{lastLocationUpdate.toLocaleTimeString()}</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Recording {recording?.coordinates.length || 0} GPS points
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex justify-center space-x-4">
        {!isRecording ? (
          <button
            onClick={() => setShowStartModal(true)}
            className="flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            <Play className="h-5 w-5" />
            <span>Start Recording</span>
          </button>
        ) : (
          <>
            <button
              onClick={isPaused ? resumeRecording : pauseRecording}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                isPaused
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-yellow-600 text-white hover:bg-yellow-700'
              }`}
            >
              {isPaused ? <Play className="h-5 w-5" /> : <Pause className="h-5 w-5" />}
              <span>{isPaused ? 'Resume' : 'Pause'}</span>
            </button>

            <button
              onClick={handleStopRecording}
              className="flex items-center space-x-2 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
            >
              <Square className="h-5 w-5" />
              <span>Stop & Save</span>
            </button>
          </>
        )}
      </div>

      {/* Offline Warning */}
      {!isOnline && (
        <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="flex items-center space-x-2 text-orange-800">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Offline Mode</span>
          </div>
          <p className="text-xs text-orange-700 mt-1">
            Recording will continue offline. Data will sync when connection is restored.
          </p>
        </div>
      )}

      {/* Start Modal */}
      {showStartModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Start Trail Recording</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trail Name *
                </label>
                <input
                  type="text"
                  value={trailName}
                  onChange={(e) => setTrailName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter trail name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={trailDescription}
                  onChange={(e) => setTrailDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows={3}
                  placeholder="Describe your trail..."
                />
              </div>
            </div>

            <div className="flex space-x-4 mt-6">
              <button
                onClick={() => setShowStartModal(false)}
                className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleStartRecording}
                disabled={!trailName.trim()}
                className="flex-1 py-2 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300"
              >
                Start Recording
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Completion Modal */}
      {showCompletionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md text-center">
            <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-900 mb-2">Trail Completed!</h3>
            <p className="text-gray-600 mb-4">
              Your trail has been recorded and saved successfully.
            </p>
            <div className="space-y-2 text-sm text-gray-600 mb-6">
              <div>Distance: {formatDistance(stats.totalDistance)}</div>
              <div>Duration: {formatDuration(stats.totalDuration)}</div>
              <div>TREK Tokens Earned: {Math.floor(stats.totalDistance / 1000) * 10}</div>
            </div>
            <button
              onClick={() => setShowCompletionModal(false)}
              className="w-full py-2 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Continue
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
