'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { CompletedTrail } from '@/types/trail'

interface DashboardStats {
  totalTrails: number
  totalDistance: number
  totalTime: number
  trekTokens: number
  totalBookings: number
  completedTrails: number
  pendingBookings: number
}

interface Booking {
  id: string
  trailId: string
  trailName: string
  date: string
  participants: number
  totalPrice: number
  bookedAt: string
  status: 'confirmed' | 'cancelled' | 'completed'
}

// CompletedTrail interface imported from types

export function useRealTimeDashboard() {
  const { user, isAuthenticated } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalTrails: 0,
    totalDistance: 0,
    totalTime: 0,
    trekTokens: 0,
    totalBookings: 0,
    completedTrails: 0,
    pendingBookings: 0
  })
  const [bookings, setBookings] = useState<Booking[]>([])
  const [completedTrails, setCompletedTrails] = useState<CompletedTrail[]>([])
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const loadDashboardData = useCallback(() => {
    if (!isAuthenticated || !user) {
      setLoading(false)
      return
    }

    try {
      // Load bookings
      const allBookings = JSON.parse(localStorage.getItem('vintrek_bookings') || '[]')
      setBookings(allBookings)

      // Load completed trails
      const allCompletedTrails = JSON.parse(localStorage.getItem('vintrek_completed_trails') || '[]')
      setCompletedTrails(allCompletedTrails)

      // Calculate stats
      const totalBookings = allBookings.length
      const pendingBookings = allBookings.filter((b: Booking) => b.status === 'confirmed').length
      const completedTrailsCount = allCompletedTrails.length
      
      const totalDistance = allCompletedTrails.reduce((sum: number, trail: CompletedTrail) => 
        sum + (trail.distance || 0), 0
      )
      
      const totalTime = allCompletedTrails.reduce((sum: number, trail: CompletedTrail) => 
        sum + (trail.duration || 0), 0
      )
      
      const trekTokens = allCompletedTrails.reduce((sum: number, trail: CompletedTrail) => 
        sum + (trail.trekTokensEarned || 0), 0
      )

      setStats({
        totalTrails: completedTrailsCount,
        totalDistance: Math.round(totalDistance / 1000), // Convert to km
        totalTime: Math.round(totalTime / 3600), // Convert to hours
        trekTokens,
        totalBookings,
        completedTrails: completedTrailsCount,
        pendingBookings
      })

      setLastUpdate(new Date())
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated, user])

  // Load data on mount and when user changes
  useEffect(() => {
    loadDashboardData()
  }, [loadDashboardData])

  // Set up real-time updates by listening to localStorage changes
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'vintrek_bookings' || e.key === 'vintrek_completed_trails') {
        console.log('📊 Dashboard: Detected data change, refreshing...')
        loadDashboardData()
      }
    }

    // Listen for storage changes from other tabs/windows
    window.addEventListener('storage', handleStorageChange)

    // Set up periodic refresh every 30 seconds
    const interval = setInterval(() => {
      loadDashboardData()
    }, 30000)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      clearInterval(interval)
    }
  }, [loadDashboardData])

  // Manual refresh function
  const refresh = useCallback(() => {
    setLoading(true)
    loadDashboardData()
  }, [loadDashboardData])

  // Get recent bookings (last 5)
  const getRecentBookings = useCallback(() => {
    return bookings
      .sort((a, b) => new Date(b.bookedAt).getTime() - new Date(a.bookedAt).getTime())
      .slice(0, 5)
  }, [bookings])

  // Get recent completed trails (last 5)
  const getRecentCompletedTrails = useCallback(() => {
    return completedTrails
      .sort((a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime())
      .slice(0, 5)
  }, [completedTrails])

  // Get upcoming bookings
  const getUpcomingBookings = useCallback(() => {
    const today = new Date()
    return bookings
      .filter(booking => {
        const bookingDate = new Date(booking.date)
        return bookingDate >= today && booking.status === 'confirmed'
      })
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 3)
  }, [bookings])

  // Check if data is fresh (updated within last 5 minutes)
  const isDataFresh = useCallback(() => {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    return lastUpdate > fiveMinutesAgo
  }, [lastUpdate])

  return {
    stats,
    bookings,
    completedTrails,
    loading,
    lastUpdate,
    refresh,
    getRecentBookings,
    getRecentCompletedTrails,
    getUpcomingBookings,
    isDataFresh,
    isAuthenticated
  }
}
